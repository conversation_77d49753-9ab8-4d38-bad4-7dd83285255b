<template>
  <view>
    <view class="form-box" v-show="show">
      <view class="form-box-title">修改人员</view>
      <view class="section-title">飞行部</view>
      <FormItem label="机长：" label-width="70px">
        <input
            v-model="formNameData.registrationNumber"
            placeholder="请选择"
            disabled
            @click="openPicker('注册号', '', 'registrationNumber')"
        />
      </FormItem>
      <FormItem label="副机长：" label-width="70px">
        <input
            v-model="formNameData.registrationNumber"
            placeholder="请选择"
            disabled
            @click="openPicker('注册号', '', 'registrationNumber')"
        />
      </FormItem>
      <view class="section-title">机务部</view>
      <FormItem label="机械师：" label-width="70px">
        <input
            v-model="formNameData.registrationNumber"
            placeholder="请选择"
            disabled
            @click="openPicker('注册号', '', 'registrationNumber')"
        />
      </FormItem>
      <FormItem label="机械员：" label-width="70px">
        <input
            v-model="formNameData.registrationNumber"
            placeholder="请选择"
            disabled
            @click="openPicker('注册号', '', 'registrationNumber')"
        />
      </FormItem>
      <FormItem label="保障员：" label-width="70px">
        <input
            v-model="formNameData.registrationNumber"
            placeholder="请选择"
            disabled
            @click="openPicker('注册号', '', 'registrationNumber')"
        />
      </FormItem>
      <view class="section-title">运控中心</view>
      <FormItem label="安检：" label-width="70px">
        <input
            v-model="formNameData.registrationNumber"
            placeholder="请选择"
            disabled
            @click="openPicker('注册号', '', 'registrationNumber')"
        />
      </FormItem>
      <FormItem label="现场组织：" label-width="70px">
        <input
            v-model="formNameData.registrationNumber"
            placeholder="请选择"
            disabled
            @click="openPicker('注册号', '', 'registrationNumber')"
        />
      </FormItem>
      <FormItem label="责任运控：" label-width="70px">
        <input
            v-model="formNameData.registrationNumber"
            placeholder="请选择"
            disabled
            @click="openPicker('注册号', '', 'registrationNumber')"
        />
      </FormItem>
      <FormItem label="运控助理：" label-width="70px">
        <input
            v-model="formNameData.registrationNumber"
            placeholder="请选择"
            disabled
            @click="openPicker('注册号', '', 'registrationNumber')"
        />
      </FormItem>
      <FormItem label="随机人数：" label-width="70px">
        <input
            v-model="formNameData.registrationNumber"
            placeholder="请输入"
        />
      </FormItem>
      <view class="section-title">市场部</view>
      <FormItem label="售票：" label-width="70px">
        <input
            v-model="formNameData.registrationNumber"
            placeholder="请选择"
            disabled
            @click="openPicker('注册号', '', 'registrationNumber')"
        />
      </FormItem>
      <FormItem label="行李数：" label-width="70px">
        <input
            v-model="formNameData.registrationNumber"
            placeholder="请选择"
        />
      </FormItem>
    </view>
    <van-popup :show="pickerData.show" position="bottom">
      <view class="popup-header">
        <text class="cancel" @click="closePicker">取消</text>
        <text class="title">{{ pickerData.title }}</text>
        <text class="confirm" @click="confirmPicker">确认</text>
      </view>
      <view class="popup-content">
        <van-checkbox-group @change="onChange" :value="formCheckedIdData">
          <van-cell
              v-for="(item,index) in pickerData.list"
              :key="index"
              :title="item.name"
              clickable
              @click="toggle"
              value-class="value-class"
              title-class="title-class"
              custom-class="custom-class"
          >
            <van-checkbox
                :name="item.name"
                :value="item.id"
                shape="square"
            />
          </van-cell>
        </van-checkbox-group>
      </view>
    </van-popup>
  </view>
</template>
<script>
import FormItem from "./FormItem.vue";

export default {
  name: "StaffModal",
  components: {FormItem},
  props: {
    title: {
      type: String,
      default: "修改人员"
    },
    show: {
      type: Boolean,
      default: false
    },
    onClose: {
      type: Function,
      default: () => {
      }
    },
    onConfirm: {
      type: Function,
      default: () => {
      }
    }
  },
  data() {
    return {
      // formCheckedNameData: [],
      formCheckedIdData: [],//选择id的数据
      //下拉选择数据
      pickerData: {
        show: false,
        title: "",
        list: [],
        formKey: "",
      },
      formNameData: {},//表单名称数据展示用
      formIdData: {},//表单id数据

    };
  },
  methods: {
    openPicker(title, list, formKey) {
      this.pickerData = {
        show: true,
        title: title,
        list: list || [{name: "1111", value: 11}, {name: "2222", value: 22}, {name: "3333", value: 33}],
        formKey: formKey,
      }
    },
    closePicker() {
      this.pickerData = {
        show: false,
        title: "",
        list: [],
        formKey: "",
      }
    },
    confirmPicker() {
      const {formKey, list} = this.pickerData;
      const value = list.filter(item => item.checked).map(item => item.value);
    },
    toggle(event) {
      // const {index} = event.currentTarget.dataset;
      // const checkbox = this.selectComponent(`.checkboxes-${index}`);
      // checkbox.toggle();
    },
    onChange(event) {
      console.log(event.detail)

      this.formIdData = event.detail;
    },
  }
}
</script>


<style scoped lang="scss">
.form-box {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

}

.form-box-title {
  font-weight: bold;
  font-size: 16px;
  text-align: center;
  margin-bottom: 16px;
}

.section-title {
  font-weight: bold;
  font-size: 16px;
  padding: 12px 0 0 0;
  position: relative;

  &::before {
    content: '';
    display: block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #333;
    position: absolute;
    left: -10px;
    top: calc(50% + 2px);
    //transform: translateY(-50%);
  }
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  box-sizing: border-box;

  .cancel {
    color: #999;
    font-size: 12px;
  }

  .confirm {
    color: #2C5DE5;
    font-size: 12px;
  }

  .title {
    font-size: 14px;
    font-weight: bold;
    color: #333;
  }
}
</style>
<style lang="scss">
.popup-content {
  padding: 16px;
  box-sizing: border-box;

  .custom-class {
    display: flex;
    justify-content: space-between;
  }

  .value-class {
    text-align: right;
  }

  .title-class, .value-class {
    width: auto;
    flex: none;
  }

}
</style>
