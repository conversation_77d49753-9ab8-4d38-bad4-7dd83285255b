<template>
  <view class="receive-detail-page">
    <!-- 自定义导航栏 -->
    <CustomerNav title="飞行任务确认" />

    <!-- 内容主体 -->
    <view class="content">
      <!-- 表单区域 -->
      <view class="form-box">
        <FormItem label="注册号：" class="text-row">
          <text>B-7613</text>
        </FormItem>

        <FormItem label="机型：" class="text-row">
          <text>BELL429</text>
        </FormItem>

        <FormItem label="任务性质：" class="text-row">
          <text>空中游览</text>
        </FormItem>

        <FormItem label="计划时间：" class="text-row">
          <text>2025-07-12（今天）</text>
        </FormItem>

        <FormItem
          label="产品信息："
          class="text-row"
          v-if="formData.taskType.indexOf('空中游览') > -1"
        >
          <text>黄浦江上海滨江升机游览空中游览【日间】15分钟</text>
        </FormItem>
        <view v-else>
          <FormItem label="起飞基地：" class="text-row">
            <text>星野基地</text>
          </FormItem>
          <FormItem label="降落基地：" class="text-row">
            <text>星野基地</text>
          </FormItem>
        </view>

        <FormItem label="预估架次：" class="text-row">
          <text>3</text>
        </FormItem>

        <FormItem label="市场备注：" class="text-row">
          <text>有外国人</text>
        </FormItem>

        <FormItem label="计划起降时间" multi-line>
          <view>
            <FormItem
              :label="'架次' + (index + 1)"
              label-width="74px"
              is-child
              v-for="(item, index) in formData.takeOffAndLanding"
              :key="index"
              class="text-row"
            >
              <view class="flex-row">
                <input
                  class="input-box"
                  v-model="item.planDepartTime"
                  placeholder="预计起飞时间"
                />
                <input
                  class="input-box"
                  v-model="item.planArriveTime"
                  placeholder="预计降落时间"
                />
              </view>
            </FormItem>
          </view>
        </FormItem>
        <view v-if="formData.taskType.indexOf('空中游览') > -1">
          <FormItem label="空游产品名称" show-icon>
            <text>{{ formData.productName }}</text>
          </FormItem>
          <FormItem label="套餐名称" show-icon>
            <text>{{ formData.packageName }}</text>
          </FormItem>
        </view>
        <view v-else>
          <FormItem label="航线类型">
            <view class="btn-groups">
              <view
                class="custom-tag"
                v-for="item in routeTypeList"
                :class="formData.flightType === item.value ? 'active' : ''"
                :key="item.value"
                @click="onRouteTypeChange(item)"
              >
                {{ item.text }}
              </view>
            </view>
          </FormItem>
          <view v-if="formData.flightType === 1">
            <FormItem
              label="航线选择"
              show-icon
              clearable
              @clear="onRouteClear"
            >
              <input
                class="input-box"
                v-model="formData.routeOrAirspaceName.text"
                placeholder="请选择航线"
                disabled
                @click="openPicker('航线', routeOptions, 'routeOrAirspaceName')"
              />
            </FormItem>
            <FormItem
              label="航线手动输入"
              multi-line
              v-if="!formData.routeOrAirspaceName.text"
            >
              <view>
                <FormItem label="起飞基地" is-child label-width="74px">
                  <view class="flex-row">
                    <input
                      class="input-box"
                      v-model="formData.departure.text"
                      placeholder="请选择"
                      disabled
                      @click="openPicker('起飞基地', baseOptions, 'departure')"
                    />
                  </view>
                </FormItem>
                <FormItem label="经停点" is-child label-width="74px">
                  <view class="flex-row">
                    <input
                      class="input-box"
                      v-model="formData.alternate.text"
                      placeholder="请选择"
                      disabled
                      @click="openPicker('经停点', baseOptions, 'alternate')"
                    />
                  </view>
                </FormItem>
                <FormItem label="降落基地" is-child label-width="74px">
                  <view class="flex-row">
                    <input
                      class="input-box"
                      v-model="formData.arrive.text"
                      placeholder="请选择"
                      disabled
                      @click="openPicker('降落基地', baseOptions, 'arrive')"
                    />
                  </view>
                </FormItem>
              </view>
            </FormItem>
          </view>
          <FormItem label="空域选择" required show-icon v-else>
            <input
              class="input-box"
              v-model="formData.routeOrAirspaceName.text"
              placeholder="请选择空域"
              disabled
              @click="
                openPicker('空域', airspaceOptions, 'routeOrAirspaceName')
              "
            />
          </FormItem>

          <FormItem label="高度">
            <input
              class="input-box"
              v-model="formData.routeOrAirspaceName"
              placeholder="请输入高度"
            />
          </FormItem>
          <FormItem label="备降场" show-icon>
            <view class="flex-row">
              <input
                class="input-box"
                v-model="formData.arrive"
                placeholder="请选择"
                @click="openPicker('备降场', baseOptions, 'routeOrAirspaceId')"
              />
            </view>
          </FormItem>
          <FormItem label="飞行规则">
            <input
              class="input-box"
              v-model="formData.routeOrAirspaceName"
              placeholder="请输入飞行规则"
            />
          </FormItem>
          <FormItem label="机长气象标准">
            <input
              class="input-box"
              v-model="formData.routeOrAirspaceName"
              placeholder="请输入机长气象标准"
            />
          </FormItem>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-section">
        <van-button type="info" block @click="submitForm"
          >确定保障计划
        </van-button>
      </view>
      <!-- pop弹窗-->
      <van-popup :show="pickerData.show" position="bottom">
        <van-picker
          :columns="pickerData.list"
          @confirm="onPickerConfirm"
          @cancel="closePicker"
          show-toolbar
          :title="pickerData.title"
        />
      </van-popup>
    </view>
  </view>
</template>

<script>
import CustomerNav from '../../components/CutomerNav/index.vue'
import FormItem from './compoents/FormItem.vue'
import dayjs from 'dayjs'
import {
  getAircraftStyle,
  getAirportAll,
  getAirspaceAll,
  getFlightPurpose,
  getRouteAll,
  queryFlightTaskConfig,
  queryFlightTaskConfigDetail,
} from '../../api/flightTask'
import { SUCCESS_CODE } from '../../utils/constant'

export default {
  name: 'sureDetail',
  components: { CustomerNav, FormItem },
  data() {
    return {
      taskId: '',
      formData: {
        registrationNumber: '', // 注册号
        aircraftType: '', // 机型
        taskType: '空中交通', // 任务性质
        taskTypeValue: '空中交通', //任务性质选中按钮的值--页面展示用
        flightDate: '', //计划时间
        flightDateValue: '', //计划时间选中按钮的值--页面展示用
        timeInterval: '', //  起降间隔--页面展示用
        productName: '', //产品名称
        packageName: '', //套餐名称
        flightFrequency: 1, //架次
        takeOffAndLanding: [{ planArriveTime: '', planDepartTime: '' }], //起降时间
        remark: '',
        flightType: 1, //航线类型
        routeOrAirspaceName: { text: '', value: '' }, //航线选择
        departure: { text: '', value: '' }, //起飞基地
        alternate: { text: '', value: '' }, //经停点
        arrive: { text: '', value: '' }, //降落基地
      },
      //下拉选择数据
      pickerData: {
        show: false,
        title: '',
        list: [],
        formKey: '',
      },
      //航线类型
      routeTypeList: [
        { text: '航线', value: 1 },
        { text: '空域', value: 2 },
      ],
      routeOptions: [], //航线
      airspaceOptions: [], //空域
      baseOptions: [], //起飞/降落/经停点基地
    }
  },
  mounted() {
    this.getData()
  },
  watch: {
    'formData.flightFrequency'(newVal) {
      this.formData.takeOffAndLanding = Array.from(
        { length: Number(newVal) },
        () => ({
          planArriveTime: '',
          planDepartTime: '',
        })
      )
    },
  },
  onLoad: function (option) {
    this.taskId = option.id
  },
  methods: {
    // 初始化数据
    async getData() {
      const res = await queryFlightTaskConfigDetail({
        flightTaskConfigId: Number(this.taskId),
      })
      if (res.response.code === 200) {
        // this.taskList = res.response.data
        this.formData = res.response.data || {}
      }
    },
    //获取下拉数据
    async getPickerListData() {
      //航线
      if (this.formData.flightType === 1) {
        const res3 = await getRouteAll()
        if (res3.response.code === SUCCESS_CODE) {
          this.routeOptions = res3.response.data.map((item) => {
            return {
              text: item.routeCode || '',
              value: item.id || '',
            }
          })
        }
        const res5 = await getAirportAll()
        if (res5.response.code === SUCCESS_CODE) {
          this.baseOptions = res5.response.data.map((item) => {
            return {
              text: item.name || '',
              value: item.threeAirportCode || '',
            }
          })
        }
      }
      //空域
      if (this.formData.flightType === 2) {
        const res4 = await getAirspaceAll()
        if (res4.response.code === SUCCESS_CODE) {
          this.airspaceOptions = res4.response.data.map((item) => {
            return {
              text: item.airspaceName || '',
              value: item.id || '',
            }
          })
        }
      }
    },
    //航线清除
    onRouteClear() {
      this.formData.routeOrAirspaceName = { text: '', value: '' }
    },

    openPicker(title, list, formKey) {
      this.pickerData = {
        show: true,
        title: title,
        list: list,
        formKey: formKey,
      }
    },
    closePicker() {
      this.pickerData = {
        show: false,
        title: '',
        list: [],
        formKey: '',
      }
    },
    onPickerConfirm(ev) {
      this.formData[this.pickerData.formKey] = ev.detail.value.value
      if (this.pickerData.formKey === 'taskType') {
        this.formData.taskType = ev.detail.value.text
      }
      this.closePicker()
    },
    //航线类型
    onRouteTypeChange(item) {
      this.formData.flightType = item.value
      this.formData.routeOrAirspaceId = ''
      this.formData.routeOrAirspaceName = ''
      this.formData.departure = ''
      this.formData.alternate = ''
      this.formData.arrive = ''
    },

    // 提交表单
    submitForm() {
      // 表单验证
      if (!this.validateForm()) {
        return
      }

      // 构建提交数据
      const submitData = {
        ...this.formData,
        route: this.routeOptions[this.routeIndex],
        airspace: this.airspaceOptions[this.airspaceIndex],
        landingBase: this.landingBaseOptions[this.landingBaseIndex],
        alternate: this.alternateOptions[this.alternateIndex],
      }

      console.log('提交数据:', submitData)

      uni.showToast({
        title: '创建计划成功',
        icon: 'success',
      })

      // 延迟返回
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    },

    // 表单验证
    validateForm() {
      if (!this.formData.departureTime1) {
        uni.showToast({
          title: '请填写计划起飞时间',
          icon: 'none',
        })
        return false
      }

      if (!this.formData.flightRules) {
        uni.showToast({
          title: '请填写飞行规则',
          icon: 'none',
        })
        return false
      }

      return true
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../assets/css/common.less';

.receive-detail-page {
  background-color: #f7f7f7;
  min-height: 100vh;
}

.content {
  padding: 16px;
}

/* 表单样式 */
.form-box {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  /deep/ .text-row {
    .value-box {
      border-bottom: none;
    }

    .input-box {
      border-bottom: 1px solid #ccc;
    }
  }

  .flex-row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 6px;
  }

  .btn-groups {
    width: 100%;
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
  }

  .custom-tag {
    padding: 0 4px;
    height: 24px;
    line-height: 24px;
    border: 1px solid #1989fa;
    color: #1989fa;
    background: transparent;
    font-size: 12px;
    border-radius: 4px;

    &.active {
      background: #1989fa;
      color: #fff;
    }
  }
}

.submit-btn {
  width: 100%;
  margin-top: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
