<template>
  <view class="receive-detail-page">
    <!-- 自定义导航栏 -->
    <CustomerNav title="市场任务接收"/>

    <!-- 内容主体 -->
    <view class="content">
      <!-- 表单区域 -->
      <view class="form-box">
        <FormItem label="注册号：" class="text-row">
          <text>B-7613</text>
        </FormItem>

        <FormItem label="机型：" class="text-row">
          <text>BELL429</text>
        </FormItem>

        <FormItem label="任务性质：" class="text-row">
          <text>空中游览</text>
        </FormItem>

        <FormItem label="计划时间：" class="text-row">
          <text>2025-07-12（今天）</text>
        </FormItem>

        <FormItem label="起飞基地：" class="text-row">
          <text>星野基地</text>
        </FormItem>
        <FormItem label="经停点：" class="text-row">
          <text>星野基地</text>
        </FormItem>
        <FormItem label="降落基地：" class="text-row">
          <text>星野基地</text>
        </FormItem>

        <FormItem label="计划起飞：" class="text-row">
          <text>星野基地</text>
        </FormItem>
        <FormItem label="计划到达：" class="text-row">
          <text>有外国人</text>
        </FormItem>
        <FormItem label="高度：" class="text-row">
          <text>有外国人</text>
        </FormItem>
        <FormItem label="备降场：" class="text-row">
          <text>有外国人</text>
        </FormItem>
        <FormItem label="飞行规则：" class="text-row">
          <text>有外国人</text>
        </FormItem>
        <FormItem label="机长气象标准：" class="text-row">
          <text>有外国人</text>
        </FormItem>

        <FormItem label="航线/空域：" class="text-row">
          <text>有外国人</text>
        </FormItem>
        <FormItem label="市场备注：" class="text-row">
          <text>有外国人</text>
        </FormItem>
        <FormItem label="运控备注：" class="text-row">
          <text>有外国人</text>
        </FormItem>
      </view>
      <!-- 提交按钮 -->
      <view class="submit-btn-box">
        <van-button type="info" @click="submitForm">确认接收</van-button>
        <van-button type="info" plain @click="staffModalShow = !staffModalShow">修改人员</van-button>
      </view>
      <!-- 人员选择弹窗 -->
      <StaffModal :show="staffModalShow"/>
    </view>


  </view>
</template>

<script>
import CustomerNav from "../../components/CutomerNav/index.vue";
import FormItem from "./compoents/FormItem.vue";
import dayjs from 'dayjs';
import StaffModal from "./compoents/StaffModal.vue";

export default {
  name: "sureDetail",
  components: {StaffModal, CustomerNav, FormItem},
  data() {
    return {
      formData: {
        registrationNumber: '', // 注册号
        aircraftType: '', // 机型
        taskType: '空中交通', // 任务性质
        taskTypeValue: "空中交通",//任务性质选中按钮的值--页面展示用
        flightDate: "",//计划时间
        flightDateValue: "",//计划时间选中按钮的值--页面展示用
        timeInterval: "",//  起降间隔--页面展示用
        productName: "",//产品名称
        packageName: "",//套餐名称
        flightFrequency: 1,//架次
        takeOffAndLanding: [{planArriveTime: "", planDepartTime: ""}],//起降时间
        remark: "",
        flightType: 1,//航线类型
        routeOrAirspaceId: "", //航线选择
        routeOrAirspaceName: "", //航线选择
        departure: "",//起飞基地
        alternate: "",//经停点
        arrive: "",//降落基地
      },
      staffModalShow: true, // 人员选择弹窗
    }
  },
  mounted() {
    this.initData();
  },
  methods: {
    // 初始化数据
    initData() {
      console.log('初始化接受页详情数据');
    },


    // 提交表单
    submitForm() {
      // 表单验证
      if (!this.validateForm()) {
        return;
      }

      // 构建提交数据
      const submitData = {
        "aircraftType": "BELL505",
        "alternate": "星野基地",
        "arrive": "星野基地",
        "departure": "星野基地",
        "flightDate": "2025-08-06",
        "flightFrequency": 2,
        "flightType": 1,
        "packageName": "【日间】15分钟",
        "productName": "【星野基地】直升机浏览",
        "registrationNumber": "B-707T",
        "remark": "备注",
        "routeOrAirspaceId": "1;2;3",
        "routeOrAirspaceName": "迪斯尼空域一;迪斯尼空域二;迪斯尼空域三",
        "takeOffAndLanding": [
          {
            "planArriveTime": "1000",
            "planDepartTime": "0900"
          }
        ],
        "taskType": "空中拍照"
      };

      console.log('提交数据:', submitData);

      uni.showToast({
        title: '创建计划成功',
        icon: 'success'
      });

      // 延迟返回
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    },

    // 表单验证
    validateForm() {
      if (!this.formData.departureTime1) {
        uni.showToast({
          title: '请填写计划起飞时间',
          icon: 'none'
        });
        return false;
      }

      if (!this.formData.flightRules) {
        uni.showToast({
          title: '请填写飞行规则',
          icon: 'none'
        });
        return false;
      }

      return true;
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../assets/css/common.less";

.receive-detail-page {
  background-color: #f7f7f7;
  min-height: 100vh;
}

.content {
  padding: 16px;
}

/* 表单样式 */
.form-box {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  /deep/ .text-row {
    .value-box {
      border-bottom: none;
      padding: 0;
    }

    .label-box {
      padding: 0;
    }

    .input-box {
      border-bottom: 1px solid #ccc;
    }
  }

  .flex-row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 6px;
  }
}

.submit-btn-box {
  width: 100%;
  margin: 16px auto;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}


</style>

