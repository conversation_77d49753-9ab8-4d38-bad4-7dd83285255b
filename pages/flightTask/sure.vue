<template>
  <view class="receive-page">
    <!-- 自定义导航栏 -->
    <CustomerNav :title="taskStatus === '1' ? '待确认任务' : '待接收任务'" />

    <!-- 任务列表 -->
    <view class="task-list">
      <view
        class="task-card"
        v-for="(task, index) in taskList"
        :key="index"
        @click="handleTaskClick(task)"
      >
        <view class="task-info">
          <view class="info-row">
            <view class="info-item">
              <text class="label">注册号：</text>
              <text class="value">{{ task.registrationNumber }}</text>
            </view>
            <view class="info-item">
              <text class="label">任务性质：</text>
              <text class="value">{{ task.taskType }}</text>
            </view>
          </view>

          <view class="info-row">
            <view class="info-item">
              <text class="label">起飞基地：</text>
              <text class="value">{{ task.departure }}</text>
            </view>
          </view>

          <view class="info-row" v-if="task.arrive">
            <view class="info-item">
              <text class="label">降落基地：</text>
              <text class="value">{{ task.arrive }}</text>
            </view>
          </view>

          <view class="info-row" v-if="task.alternate">
            <view class="info-item full-width">
              <text class="label">经停基地：</text>
              <text class="value">{{ task.alternate }}</text>
            </view>
          </view>

          <view class="info-row">
            <view class="info-item full-width">
              <text class="label">飞行日期：</text>
              <text class="value">{{ task.flightDate }}</text>
            </view>
          </view>

          <view class="info-row">
            <view class="info-item">
              <text class="label">预估架次：</text>
              <text class="value">{{ task.flightFrequency }}</text>
            </view>
          </view>

          <view class="info-row">
            <view class="info-item full-width">
              <text class="label">市场部备注：</text>
              <text class="value">{{ task.remark }}</text>
            </view>
          </view>

          <view class="publish-time">
            <text>发布时间：{{ task.createTime }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import CustomerNav from '../../components/CutomerNav/index.vue'
import dayjs from 'dayjs'
import { queryFlightTaskConfig } from '../../api/flightTask'

export default {
  name: 'sure',
  components: { CustomerNav },
  data() {
    return {
      taskStatus: '', //1:未确认, 2:未接收
      taskList: [],
    }
  },
  mounted() {
    this.getData()
  },
  onLoad: function (option) {
    this.taskStatus = option.status
  },
  methods: {
    // 加载任务列表
    async getData() {
      const res = await queryFlightTaskConfig({
        taskStatus: Number(this.taskStatus),
      })
      if (res.response.code === 200) {
        this.taskList = res.response.data || []
      }
    },
    handleTaskClick(item) {
      if (this.taskStatus === '1') {
        uni.navigateTo({
          url: `/pages/flightTask/sureDetail?id=${item.id}`,
        })
      } else {
        uni.navigateTo({
          url: `/pages/flightTask/receiveDetail?id=${item.id}`,
        })
      }
    },
  },
}
</script>

<style scoped>
@import '../../assets/css/common.less';

.receive-page {
  background-color: #f7f7f7;
  min-height: 100vh;
}

.task-list {
  padding: 16px;
}

.task-card {
  background-color: #ffffff;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.task-info {
  padding: 20px;
}

.info-row {
  display: flex;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.info-row:last-of-type {
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 50%;
  margin-bottom: 8px;
}

.info-item.full-width {
  flex: 1;
  min-width: 100%;
}

.label {
  color: #666666;
  font-size: 14px;
  margin-right: 4px;
  white-space: nowrap;
}

.value {
  color: #333333;
  font-size: 14px;
  font-weight: 500;
}

.publish-time {
  text-align: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  margin-top: 8px;
}

.publish-time text {
  color: #666666;
  font-size: 12px;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .info-item {
    min-width: 100%;
  }

  .task-info {
    padding: 16px;
  }
}
</style>
