import api from './index.js'

//获取机型
export const getAircraftStyle = (params) =>
  api._post('/wechat/Aircraft/queryAll', {
    params,
  })
//查询所有航线信息
export const getRouteAll = (body) =>
  api._post('/wechat/route/queryAll', {
    body,
  })
//查询所有空域信息
export const getAirspaceAll = (body) =>
  api._post('/wechat/airspace/queryAll', {
    body,
  }) //查询所有空域信息
export const getAirportAll = (body) =>
  api._post('/wechat/airport/queryAll', {
    body,
  })
//查询任务类型
export const getFlightPurpose = (body) =>
  api._post('/wechat/flightPurpose/queryAll', {
    body,
  })
//新增预填飞行任务书
export const addFlightTask = (body) =>
  api._post('/wechat/flightTaskConfig/add', {
    body,
  })
//通过ID查询单条飞行任务书
export const getFlightTaskById = (id, body) =>
  api._post(`/wechat/flightTaskConfig/getOne/${id}`, {
    body,
  })
//解析文本
export const parseTextFlightTask = (body) =>
  api._post('/wechat/flightTaskConfig/parseText', {
    body,
  })
//任务书内容确认
export const planConfirmFlightTask = (body) =>
  api._post('/wechat/flightTaskConfig/planConfirm', {
    body,
  })
//任务书接收内容确认
export const receiveConfirmFlightTask = (body) =>
  api._post('/wechat/flightTaskConfig/receiveConfirm', {
    body,
  })
//修改机组人员
export const updateCrewFlightTask = (body) =>
  api._post('/wechat/flightTaskConfig/updateCrew', {
    body,
  })
//查询预填飞行任务书详情
export const queryFlightTaskConfigDetail = (body) =>
  api._post('/wechat/flightTaskConfig/queryFlightTaskConfigDetail', {
    body,
  })
//查询预填飞行任务书列表
export const queryFlightTaskConfig = (body) =>
  api._post('/wechat/flightTaskConfig/queryFlightTaskConfig', {
    body,
  })
